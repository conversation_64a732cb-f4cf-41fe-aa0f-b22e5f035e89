---
marp: true
theme: default
paginate: true
backgroundColor: #f8f9fa
color: #333
header: 'Python Functions Course'
footer: 'Step-by-Step Learning Guide'
---

# Python Functions Course
## A Complete Step-by-Step Guide

### Learn to master Python functions from basics to advanced concepts

---

# Course Overview

## What You'll Learn
- ✅ Function basics and syntax
- ✅ Parameters and arguments
- ✅ Return values and scope
- ✅ Advanced function concepts
- ✅ Best practices and real-world applications

## Prerequisites
- Basic Python syntax knowledge
- Understanding of variables and data types

---

# Table of Contents

1. **Introduction to Functions**
2. **Basic Function Syntax**
3. **Parameters and Arguments**
4. **Return Values**
5. **Variable Scope**
6. **Default Parameters**
7. **Keyword Arguments**
8. **Variable-Length Arguments**
9. **Lambda Functions**
10. **Advanced Topics**
11. **Best Practices**
12. **Practice Exercises**

---

# Chapter 1: Introduction to Functions

## What is a Function?
A function is a **reusable block of code** that performs a specific task.

## Why Use Functions?
- 🔄 **Reusability**: Write once, use many times
- 🧩 **Modularity**: Break complex problems into smaller parts
- 🐛 **Easier debugging**: Isolate and fix issues
- 📖 **Readability**: Make code more organized and understandable

## Real-World Analogy
Think of a function like a **recipe**:
- Input: Ingredients (parameters)
- Process: Cooking steps (function body)
- Output: Finished dish (return value)

---

# Chapter 2: Basic Function Syntax

## Function Definition
```python
def function_name():
    """Optional docstring"""
    # Function body
    pass
```

## Your First Function
```python
def greet():
    """This function prints a greeting message"""
    print("Hello, World!")

# Call the function
greet()  # Output: Hello, World!
```

## Key Components
- `def`: Keyword to define a function
- `function_name`: Choose descriptive names
- `()`: Parentheses for parameters
- `:`: Colon to start function body
- Indentation: Function body must be indented

---

# Chapter 3: Parameters and Arguments

## What are Parameters?
**Parameters** are variables in function definition
**Arguments** are actual values passed to the function

```python
def greet_person(name):  # 'name' is a parameter
    print(f"Hello, {name}!")

greet_person("Alice")    # "Alice" is an argument
```

## Multiple Parameters
```python
def introduce(name, age, city):
    print(f"Hi, I'm {name}, {age} years old, from {city}")

introduce("Bob", 25, "New York")
# Output: Hi, I'm Bob, 25 years old, from New York
```

---

# Chapter 4: Return Values

## The `return` Statement
Functions can send data back using `return`

```python
def add_numbers(a, b):
    result = a + b
    return result

sum_result = add_numbers(5, 3)
print(sum_result)  # Output: 8
```

## Multiple Return Values
```python
def get_name_age():
    return "Alice", 30

name, age = get_name_age()
print(f"Name: {name}, Age: {age}")
```

## Functions Without Return
```python
def print_message():
    print("This function doesn't return anything")
    # Implicitly returns None

result = print_message()
print(result)  # Output: None
```

---

# Chapter 5: Variable Scope

## Local vs Global Scope

### Local Scope
```python
def my_function():
    local_var = "I'm local"
    print(local_var)

my_function()
# print(local_var)  # Error: local_var not accessible outside
```

### Global Scope
```python
global_var = "I'm global"

def access_global():
    print(global_var)  # Can access global variable

access_global()  # Output: I'm global
```

### Modifying Global Variables
```python
counter = 0

def increment():
    global counter
    counter += 1

increment()
print(counter)  # Output: 1
```

---

# Chapter 6: Default Parameters

## Setting Default Values
```python
def greet(name, greeting="Hello"):
    print(f"{greeting}, {name}!")

greet("Alice")           # Output: Hello, Alice!
greet("Bob", "Hi")       # Output: Hi, Bob!
```

## Multiple Default Parameters
```python
def create_profile(name, age=18, city="Unknown"):
    return f"Name: {name}, Age: {age}, City: {city}"

print(create_profile("Alice"))
print(create_profile("Bob", 25))
print(create_profile("Charlie", 30, "Paris"))
```

## Important Rule
**Default parameters must come after non-default parameters**

```python
# ✅ Correct
def func(a, b=10, c=20):
    pass

# ❌ Incorrect
def func(a=10, b, c=20):  # SyntaxError
    pass
```

---

# Chapter 7: Keyword Arguments

## Using Keyword Arguments
```python
def book_info(title, author, year, pages):
    print(f"'{title}' by {author} ({year}) - {pages} pages")

# Positional arguments
book_info("1984", "George Orwell", 1949, 328)

# Keyword arguments
book_info(author="George Orwell", title="1984", pages=328, year=1949)

# Mixed (positional first, then keyword)
book_info("1984", author="George Orwell", year=1949, pages=328)
```

## Benefits of Keyword Arguments
- 📝 **Clarity**: Makes function calls more readable
- 🔄 **Flexibility**: Change argument order
- 🛡️ **Safety**: Reduces errors from wrong argument positions

---

# Chapter 8: Variable-Length Arguments

## *args (Variable Positional Arguments)
```python
def sum_all(*numbers):
    total = 0
    for num in numbers:
        total += num
    return total

print(sum_all(1, 2, 3))        # Output: 6
print(sum_all(1, 2, 3, 4, 5))  # Output: 15
```

## **kwargs (Variable Keyword Arguments)
```python
def print_info(**info):
    for key, value in info.items():
        print(f"{key}: {value}")

print_info(name="Alice", age=30, city="Boston")
# Output:
# name: Alice
# age: 30
# city: Boston
```

## Combining All Types
```python
def flexible_function(required, *args, default="default", **kwargs):
    print(f"Required: {required}")
    print(f"Args: {args}")
    print(f"Default: {default}")
    print(f"Kwargs: {kwargs}")

flexible_function("must_have", 1, 2, 3, default="custom", extra="info")
```

---

# Chapter 9: Lambda Functions

## What are Lambda Functions?
**Lambda functions** are small, anonymous functions defined inline

## Basic Syntax
```python
# Regular function
def square(x):
    return x ** 2

# Lambda equivalent
square_lambda = lambda x: x ** 2

print(square(5))        # Output: 25
print(square_lambda(5)) # Output: 25
```

## Common Use Cases

### With `map()`
```python
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x ** 2, numbers))
print(squared)  # Output: [1, 4, 9, 16, 25]
```

### With `filter()`
```python
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
evens = list(filter(lambda x: x % 2 == 0, numbers))
print(evens)  # Output: [2, 4, 6, 8, 10]
```

### With `sorted()`
```python
students = [("Alice", 85), ("Bob", 90), ("Charlie", 78)]
sorted_by_grade = sorted(students, key=lambda x: x[1])
print(sorted_by_grade)  # Sorted by grade
```

---

# Chapter 10: Advanced Topics

## Nested Functions
```python
def outer_function(x):
    def inner_function(y):
        return x + y
    return inner_function

add_five = outer_function(5)
print(add_five(3))  # Output: 8
```

## Decorators (Introduction)
```python
def my_decorator(func):
    def wrapper():
        print("Before function call")
        func()
        print("After function call")
    return wrapper

@my_decorator
def say_hello():
    print("Hello!")

say_hello()
# Output:
# Before function call
# Hello!
# After function call
```

## Recursion
```python
def factorial(n):
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)

print(factorial(5))  # Output: 120
```

---

# Chapter 11: Best Practices

## 1. Use Descriptive Names
```python
# ❌ Bad
def calc(x, y):
    return x * y

# ✅ Good
def calculate_area(length, width):
    return length * width
```

## 2. Write Docstrings
```python
def calculate_bmi(weight, height):
    """
    Calculate Body Mass Index (BMI).
    
    Args:
        weight (float): Weight in kilograms
        height (float): Height in meters
    
    Returns:
        float: BMI value
    """
    return weight / (height ** 2)
```

## 3. Keep Functions Small and Focused
- One function should do one thing well
- Aim for functions that fit on one screen
- If a function is too long, break it into smaller functions

---

# Chapter 12: Practice Exercises

## Exercise 1: Temperature Converter
Create a function that converts Celsius to Fahrenheit
```python
def celsius_to_fahrenheit(celsius):
    # Your code here
    pass

# Test: celsius_to_fahrenheit(0) should return 32
```

## Exercise 2: Password Validator
Create a function that checks if a password is strong
```python
def is_strong_password(password):
    # Requirements:
    # - At least 8 characters
    # - Contains uppercase and lowercase
    # - Contains at least one digit
    # Your code here
    pass
```

## Exercise 3: Shopping Cart
Create functions to manage a shopping cart
```python
def add_item(cart, item, price):
    # Add item to cart
    pass

def calculate_total(cart):
    # Calculate total price
    pass

def apply_discount(total, discount_percent):
    # Apply discount
    pass
```

---

# Solutions to Exercises

## Solution 1: Temperature Converter
```python
def celsius_to_fahrenheit(celsius):
    """Convert Celsius to Fahrenheit"""
    fahrenheit = (celsius * 9/5) + 32
    return fahrenheit

# Test
print(celsius_to_fahrenheit(0))   # Output: 32.0
print(celsius_to_fahrenheit(100)) # Output: 212.0
```

## Solution 2: Password Validator
```python
def is_strong_password(password):
    """Check if password meets strength requirements"""
    if len(password) < 8:
        return False
    
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    
    return has_upper and has_lower and has_digit

# Test
print(is_strong_password("Password123"))  # True
print(is_strong_password("weak"))         # False
```

---

# Final Project: Personal Finance Calculator

## Create a comprehensive finance calculator with these functions:

```python
def calculate_monthly_payment(principal, rate, years):
    """Calculate monthly loan payment"""
    pass

def calculate_compound_interest(principal, rate, time, compound_frequency):
    """Calculate compound interest"""
    pass

def budget_analyzer(income, expenses):
    """Analyze monthly budget"""
    pass

def savings_goal(target, monthly_savings, interest_rate):
    """Calculate time to reach savings goal"""
    pass
```

## Requirements:
- Use proper docstrings
- Handle edge cases
- Include input validation
- Create a main function to tie everything together

---

# Congratulations! 🎉

## You've Completed the Python Functions Course!

### What You've Learned:
- ✅ Function basics and syntax
- ✅ Parameters, arguments, and return values
- ✅ Variable scope and default parameters
- ✅ Advanced concepts like lambda functions
- ✅ Best practices for writing clean code

### Next Steps:
1. **Practice**: Build more projects using functions
2. **Explore**: Learn about decorators and generators
3. **Apply**: Use functions in real-world projects
4. **Share**: Teach others what you've learned

### Keep Coding! 💻

---

# Resources for Further Learning

## Documentation
- [Python Official Documentation](https://docs.python.org/3/tutorial/controlflow.html#defining-functions)
- [PEP 8 Style Guide](https://pep8.org/)

## Practice Platforms
- [LeetCode](https://leetcode.com/)
- [HackerRank](https://www.hackerrank.com/)
- [Codewars](https://www.codewars.com/)

## Books
- "Clean Code" by Robert C. Martin
- "Python Tricks" by Dan Bader
- "Effective Python" by Brett Slatkin

**Happy Coding!** 🐍✨
